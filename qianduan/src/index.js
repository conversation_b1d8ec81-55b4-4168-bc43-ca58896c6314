import React from 'react';
import ReactDOM from 'react-dom/client';
import { StagewiseToolbar } from '@stagewise/toolbar-react';
import ReactPlugin from '@stagewise-plugins/react';
import { useRequest } from 'ahooks';
import init, { chushihuawangguan, get_qingqiu } from './qudong/wangluoqingqiu/wangluoqingqiu.js';
import {
  tiqu_wangzhan_jichuxinxi,
  cunchu_dao_huancun,
  cong_huancun_duqu,
  jiancha_huancun_youxiao
} from './gongju/wangzhanjichuxinxi_huancun.js';
import {
  yingyong_wangzhan_jichuxinxi,
  kuaisu_yingyong_mingcheng_tubiao
} from './gongju/wangzhan_dongtai_yingyong.js';
// 导入明暗布局组件
import {
  minganbuju,
  shiyongzhutiqiehuan,
  zhu<PERSON><PERSON><PERSON><PERSON>,
  zhu<PERSON><PERSON><PERSON>,
  zhu<PERSON><PERSON><PERSON>,
  tanxingbuju,
  donghuale<PERSON>in,
  donghuasudu,
} from './zujian/minganbuju';

const yuanshiconsole = console.log;
console.log = function(...args) {
  const message = args.join(' ');
  if (message.includes('[AgentProvider]') ||
      message.includes('[PanelsProvider]') ||
      message.includes('[PanelWrapper]') ||
      message.includes('[stagewise]')) {
    return;
  }
  yuanshiconsole.apply(console, args);
};

let chushipromise = null;
let yichushihua = false;
let wasm_yichushihua = false;

async function chushihua_wasm() {
    if (wasm_yichushihua) {
        return;
    }
    await init();
    chushihuawangguan('http://127.0.0.1:8098');
    wasm_yichushihua = true;
}

async function qianduanchushihua() {
  if (yichushihua) {
    return null;
  }
  if (chushipromise) {
    return await chushipromise;
  }

  chushipromise = (async () => {
    try {
      await chushihua_wasm();

      let wangzhan_xinxi = cong_huancun_duqu();

      if (wangzhan_xinxi) {
        kuaisu_yingyong_mingcheng_tubiao(wangzhan_xinxi);
        yingyong_wangzhan_jichuxinxi(wangzhan_xinxi);
        yichushihua = true;
        return {
          laiyuan: 'huancun',
          shuju: wangzhan_xinxi
        };
      }

      const xiangying = await get_qingqiu(
        '/jiekou/wangzhanjichuxinxi',
        null,
        false,
        false,
        5000,
        1
      );

      wangzhan_xinxi = tiqu_wangzhan_jichuxinxi(xiangying);
      if (!wangzhan_xinxi) {
        yichushihua = true;
        throw new Error('提取网站基础信息失败');
      }

      kuaisu_yingyong_mingcheng_tubiao(wangzhan_xinxi);
      cunchu_dao_huancun(wangzhan_xinxi);
      const yingyong_jieguo = yingyong_wangzhan_jichuxinxi(wangzhan_xinxi);

      yichushihua = true;
      return {
        laiyuan: 'wangluo',
        shuju: wangzhan_xinxi,
        yingyong_jieguo: yingyong_jieguo
      };

    } catch (cuowu) {
      console.error('前端初始化失败:', cuowu);
      chushipromise = null;
      throw cuowu;
    }
  })();

  return await chushipromise;
}

// 主题切换测试组件
function Zhutiqiehuanceshi() {
  const {
    qiehuandaoxiayigezhuti,
    dangqianzhuti,
    shifoianheizuti,
    qiehuanzhuti,
    zhutileixing
  } = shiyongzhutiqiehuan();

  return (
    <zhutibiaomian xuanfu style={{ marginBottom: '20px' }}>
      <zhutiwenben daxiao="da" zhongliang="cuhei">
        🌓 明暗主题测试
      </zhutiwenben>
      <zhutiwenben leixing="ciyao">
        当前主题: {dangqianzhuti} ({shifoianheizuti ? '🌙 暗黑模式' : '☀️ 明亮模式'})
      </zhutiwenben>

      <tanxingbuju style={{ marginTop: '15px', gap: '10px' }}>
        <zhutianniu
          leixing="zhuyao"
          onClick={qiehuandaoxiayigezhuti}
        >
          🔄 切换主题
        </zhutianniu>
        <zhutianniu
          leixing="biankuang"
          onClick={() => qiehuanzhuti(zhutileixing.MINGLIANG)}
        >
          ☀️ 明亮
        </zhutianniu>
        <zhutianniu
          leixing="biankuang"
          onClick={() => qiehuanzhuti(zhutileixing.ANHEI)}
        >
          🌙 暗黑
        </zhutianniu>
      </tanxingbuju>

      <zhutiwenben leixing="tishi" daxiao="xiao" style={{ marginTop: '10px' }}>
        💡 提示: 使用 Ctrl+Shift+T 快捷键快速切换主题
      </zhutiwenben>
    </zhutibiaomian>
  );
}

function App() {
  // 使用useRequest管理前端初始化
  const { loading, error } = useRequest(qianduanchushihua, {
    manual: false, // 自动执行
    cacheKey: 'wasm-init', // 缓存key，防止重复初始化
    staleTime: Infinity, // 永不过期，确保只初始化一次
  });

  return (
    <minganbuju
      donghualeixin={donghualeixin.HUADONG}
      donghuasudu={donghuasudu.ZHONGDENG}
    >
      <StagewiseToolbar config={{ plugins: [ReactPlugin] }} />

      <tanxingbuju fangxiang="lie" style={{ padding: '20px', minHeight: '100vh' }}>
        {/* 主题切换测试区域 */}
        <Zhutiqiehuanceshi />

        {/* 原有的初始化测试内容 */}
        <zhutibiaomian xuanfu>
          <zhutiwenben daxiao="dada" zhongliang="hei">
            前端初始化测试
          </zhutiwenben>

          {loading && (
            <zhutiwenben leixing="xinxi">
              ⏳ 正在初始化WASM模块...
            </zhutiwenben>
          )}

          {error && (
            <zhutiwenben leixing="cuowu">
              ❌ 初始化失败: {error.message}
            </zhutiwenben>
          )}

          {!loading && !error && (
            <zhutiwenben leixing="chenggong">
              ✅ 初始化成功！请查看控制台输出
            </zhutiwenben>
          )}
        </zhutibiaomian>

        {/* 组件功能展示 */}
        <zhutibiaomian xuanfu>
          <zhutiwenben daxiao="da" zhongliang="cuhei">
            📦 明暗布局组件功能
          </zhutiwenben>

          <zhutiwenben leixing="ciyao">
            • 🎨 自动主题切换和持久化存储
          </zhutiwenben>
          <zhutiwenben leixing="ciyao">
            • ✨ 基于 framer-motion 的流畅动画
          </zhutiwenben>
          <zhutiwenben leixing="ciyao">
            • 🎯 styled-components 主题系统
          </zhutiwenben>
          <zhutiwenben leixing="ciyao">
            • ⌨️ 键盘快捷键支持
          </zhutiwenben>
          <zhutiwenben leixing="ciyao">
            • 📱 响应式设计
          </zhutiwenben>
        </zhutibiaomian>
      </tanxingbuju>
    </minganbuju>
  );
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);
