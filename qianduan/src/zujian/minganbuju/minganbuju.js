import React from 'react';
import styled, { createGlobalStyle } from 'styled-components';
import { Zhutitiqigong, useShi<PERSON><PERSON><PERSON><PERSON><PERSON> } from './zhutitiqigong.js';
import { zhutiguodubaoguoqi, donghualeixin, donghuasudu } from './donghuaguodu.js';
import { useShiyongzhutiqiehuanluoji } from './zhutiqiehuanhook.js';

// 全局样式
const quanjuyangshi = createGlobalStyle`
  * {
    box-sizing: border-box;
  }
  
  body {
    margin: 0;
    padding: 0;
    font-family: ${props => props.theme.ziti.jiazu};
    background-color: ${props => props.theme.yanse.beijing};
    color: ${props => props.theme.yanse.wenzi_zhuyao};
    transition: background-color ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun},
                color ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: ${props => props.theme.yanse.beijing_er};
  }
  
  ::-webkit-scrollbar-thumb {
    background: ${props => props.theme.yanse.biankuang};
    border-radius: ${props => props.theme.yuanjiao.xiao};
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: ${props => props.theme.yanse.biankuang_qian};
  }
`;

// 主容器样式
const zhurognqi = styled.div`
  width: 100%;
  min-height: 100vh;
  background-color: ${props => props.theme.yanse.beijing};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
`;

// 内容区域样式
const neirongquyu = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
`;

// 明暗布局组件内部实现
const Minganbujuneibu = ({
  children,
  donghualeixin: donghualeixin_prop = donghualeixin.DANRUDANCHU,
  donghuasudu: donghuasudu_prop = donghuasudu.ZHONGDENG,
  qiyongquanjuyangshi = true,
  zidingyiyangshi = {},
  className,
  ...qitashuxing
}) => {
  const { dangqianzhuti, zhutidixiang } = useShiyongzhuti();

  return (
    <>
      {qiyongquanjuyangshi && <quanjuyangshi />}
      <zhurognqi 
        className={className}
        style={zidingyiyangshi}
        {...qitashuxing}
      >
        <zhutiguodubaoguoqi
          zhutijian={dangqianzhuti}
          donghualeixin={donghualeixin_prop}
          donghuasudu={donghuasudu_prop}
        >
          <neirongquyu>
            {children}
          </neirongquyu>
        </zhutiguodubaoguoqi>
      </zhurognqi>
    </>
  );
};

// 明暗布局主组件
export const minganbuju = ({
  children,
  chushipzhuti,
  donghualeixin: donghualeixin_prop,
  donghuasudu: donghuasudu_prop,
  qiyongquanjuyangshi,
  zidingyiyangshi,
  className,
  ...qitashuxing
}) => {
  return (
    <Zhutitiqigong chushipzhuti={chushipzhuti}>
      <Minganbujuneibu
        donghualeixin={donghualeixin_prop}
        donghuasudu={donghuasudu_prop}
        qiyongquanjuyangshi={qiyongquanjuyangshi}
        zidingyiyangshi={zidingyiyangshi}
        className={className}
        {...qitashuxing}
      >
        {children}
      </Minganbujuneibu>
    </Zhutitiqigong>
  );
};

// 明暗布局包装器（用于已有 ThemeProvider 的应用）
export const Minganbujubaoguoqi = ({
  children,
  donghualeixin: donghualeixin_prop = donghualeixin.DANRUDANCHU,
  donghuasudu: donghuasudu_prop = donghuasudu.ZHONGDENG,
  qiyongquanjuyangshi = true,
  zidingyiyangshi = {},
  className,
  ...qitashuxing
}) => {
  const zhutiqiehuanluoji = useShiyongzhutiqiehuanluoji();
  const { dangqianzhuti } = zhutiqiehuanluoji;

  return (
    <>
      {qiyongquanjuyangshi && <quanjuyangshi />}
      <zhurognqi 
        className={className}
        style={zidingyiyangshi}
        {...qitashuxing}
      >
        <zhutiguodubaoguoqi
          zhutijian={dangqianzhuti}
          donghualeixin={donghualeixin_prop}
          donghuasudu={donghuasudu_prop}
        >
          <neirongquyu>
            {children}
          </neirongquyu>
        </zhutiguodubaoguoqi>
      </zhurognqi>
    </>
  );
};
