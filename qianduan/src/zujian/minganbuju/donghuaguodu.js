import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';

// 动画类型枚举
export const donghualeixin = {
  DANRUDANCHU: 'danrudanchu',
  HUADONG: 'huadong',
  SUOFANG: 'suofang',
  XUANZHUAN: 'xuanzhuan',
  TANXING: 'tanxing',
  BOLAN: 'bolan',
};

// 动画方向枚举
export const donghuafangxiang = {
  SHANG: 'shang',
  XIA: 'xia',
  ZUO: 'zuo',
  YOU: 'you',
  ZHONGXIN: 'zhongxin',
};

// 动画速度枚举
export const donghuasudu = {
  KUAI: 'kuai',
  ZHONGDENG: 'zhongdeng',
  MAN: 'man',
  CHAOMAN: 'chaoman',
};

// 动画配置映射
const donghuapeizhi_yingshe = {
  // 淡入淡出动画
  [donghualeixin.DANRUDANCHU]: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  },
  
  // 滑动动画
  [donghualeixin.HUADONG]: {
    [donghuafangxiang.SHANG]: {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: -20 },
    },
    [donghuafangxiang.XIA]: {
      initial: { opacity: 0, y: -20 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: 20 },
    },
    [donghuafangxiang.ZUO]: {
      initial: { opacity: 0, x: 20 },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: -20 },
    },
    [donghuafangxiang.YOU]: {
      initial: { opacity: 0, x: -20 },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: 20 },
    },
  },
  
  // 缩放动画
  [donghualeixin.SUOFANG]: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 1.1 },
  },
  
  // 旋转动画
  [donghualeixin.XUANZHUAN]: {
    initial: { opacity: 0, rotate: -10 },
    animate: { opacity: 1, rotate: 0 },
    exit: { opacity: 0, rotate: 10 },
  },
  
  // 弹性动画
  [donghualeixin.TANXING]: {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.8 },
  },
  
  // 波浪动画
  [donghualeixin.BOLAN]: {
    initial: { opacity: 0, scaleX: 0.8, scaleY: 1.2 },
    animate: { opacity: 1, scaleX: 1, scaleY: 1 },
    exit: { opacity: 0, scaleX: 1.2, scaleY: 0.8 },
  },
};

// 速度配置映射
const sudupeizhiyingshe = {
  [donghuasudu.KUAI]: { duration: 0.15 },
  [donghuasudu.ZHONGDENG]: { duration: 0.3 },
  [donghuasudu.MAN]: { duration: 0.5 },
  [donghuasudu.CHAOMAN]: { duration: 0.8 },
};

// 缓动函数映射
const huanmanhanshuyingshe = {
  [donghuasudu.KUAI]: [0.4, 0, 1, 1],
  [donghuasudu.ZHONGDENG]: [0.4, 0, 0.2, 1],
  [donghuasudu.MAN]: [0.4, 0, 0.6, 1],
  [donghuasudu.CHAOMAN]: [0.25, 0.46, 0.45, 0.94],
};

// 样式化容器
const Donghuarongqi = styled(motion.div)`
  width: 100%;
  height: 100%;
`;

// 主题切换动画组件
export const zhutiqiehuandonghua = ({
  children,
  donghualeixin: leixing = donghualeixin.DANRUDANCHU,
  donghuafangxiang: fangxiang = donghuafangxiang.ZHONGXIN,
  donghuasudu: sudu = donghuasudu.ZHONGDENG,
  zidingyipeizhi = {},
  jianjian = 'wait',
  ...qitashuxing
}) => {
  // 获取动画配置
  const huoqudonghuapeizhi = () => {
    let jichupeihi;
    
    if (leixing === donghualeixin.HUADONG) {
      jichupeihi = donghuapeizhi_yingshe[leixing][fangxiang] || donghuapeizhi_yingshe[leixing][donghuafangxiang.SHANG];
    } else {
      jichupeihi = donghuapeizhi_yingshe[leixing] || donghuapeizhi_yingshe[donghualeixin.DANRUDANCHU];
    }
    
    const sudupeihi = sudupeizhiyingshe[sudu] || sudupeizhiyingshe[donghuasudu.ZHONGDENG];
    const huanmanhansu = huanmanhanshuyingshe[sudu] || huanmanhanshuyingshe[donghuasudu.ZHONGDENG];
    
    return {
      ...jichupeihi,
      transition: {
        ...sudupeihi,
        ease: huanmanhansu,
        ...zidingyipeizhi.transition,
      },
      ...zidingyipeizhi,
    };
  };

  const donghuapeizhi = huoqudonghuapeizhi();

  return (
    <AnimatePresence mode={jianjian}>
      <Donghuarongqi
        {...donghuapeizhi}
        {...qitashuxing}
      >
        {children}
      </Donghuarongqi>
    </AnimatePresence>
  );
};

// 主题过渡包装器
export const zhutiguodubaoguoqi = ({
  children,
  zhutijian,
  donghualeixin: leixing = donghualeixin.DANRUDANCHU,
  donghuafangxiang: fangxiang = donghuafangxiang.ZHONGXIN,
  donghuasudu: sudu = donghuasudu.ZHONGDENG,
  ...qitashuxing
}) => {
  return (
    <AnimatePresence mode="wait">
      <zhutiqiehuandonghua
        key={zhutijian}
        donghualeixin={leixing}
        donghuafangxiang={fangxiang}
        donghuasudu={sudu}
        {...qitashuxing}
      >
        {children}
      </zhutiqiehuandonghua>
    </AnimatePresence>
  );
};

// 高级动画组件 - 支持多层动画
export const gaojidonghuazujian = ({
  children,
  donghualiebiao = [
    { leixing: donghualeixin.DANRUDANCHU, yanchi: 0 },
  ],
  ...qitashuxing
}) => {
  return (
    <AnimatePresence mode="wait">
      {donghualiebiao.map((donghua, suoyin) => {
        const { leixing, fangxiang, sudu, yanchi = 0, ...donghuapeizhi } = donghua;
        
        return (
          <zhutiqiehuandonghua
            key={suoyin}
            donghualeixin={leixing}
            donghuafangxiang={fangxiang}
            donghuasudu={sudu}
            zidingyipeizhi={{
              transition: {
                delay: yanchi,
                ...donghuapeizhi.transition,
              },
              ...donghuapeizhi,
            }}
            {...qitashuxing}
          >
            {suoyin === donghualiebiao.length - 1 ? children : null}
          </zhutiqiehuandonghua>
        );
      })}
    </AnimatePresence>
  );
};
