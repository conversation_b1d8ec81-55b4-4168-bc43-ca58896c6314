import { useState, useEffect, useCallback } from 'react';
import { useLocalStorageState, useEventListener } from 'ahooks';
import { zhutileixing } from './zhutipeizhi.js';

// 本地存储键名
const ZHUTI_CUNCHU_JIAN = 'minganbuju_zhuti';
const ZIDONG_QIEHUAN_CUNCHU_JIAN = 'minganbuju_zidong_qiehuan';

// 主题切换逻辑 Hook
export const shiyongzhutiqiehuanluoji = (chushipzhuti = zhutileixing.MINGLIANG) => {
  // 主题状态管理
  const [dangqian<PERSON>hu<PERSON>, shezhi_dangqianzhuti] = useLocalStorageState(
    ZHUTI_CUNCHU_JIAN,
    {
      defaultValue: chushipzhuti,
    }
  );

  // 自动切换设置
  const [zidongqiehuan, shezhi_zidongqiehuan] = useLocalStorageState(
    ZIDONG_QIEHUAN_CUNCHU_JIAN,
    {
      defaultValue: false,
    }
  );

  // 动画状态
  const [qiehua<PERSON>hong, shezhi_qiehuanzhong] = useState(false);

  // 切换主题函数
  const qiehuanzhuti = useCallback((xinzhuti) => {
    if (xinzhuti && xinzhuti !== dangqianzhuti && !qiehuanzhong) {
      shezhi_qiehuanzhong(true);
      
      // 延迟切换，让动画效果更自然
      setTimeout(() => {
        shezhi_dangqianzhuti(xinzhuti);
        
        // 动画完成后重置状态
        setTimeout(() => {
          shezhi_qiehuanzhong(false);
        }, 300);
      }, 150);
    }
  }, [dangqianzhuti, qiehuanzhong, shezhi_dangqianzhuti]);

  // 切换到明亮主题
  const qiehuandaomingliangzhuti = useCallback(() => {
    qiehuanzhuti(zhutileixing.MINGLIANG);
  }, [qiehuanzhuti]);

  // 切换到暗黑主题
  const qiehuandaoanheizuti = useCallback(() => {
    qiehuanzhuti(zhutileixing.ANHEI);
  }, [qiehuanzhuti]);

  // 切换到下一个主题
  const qiehuandaoxiayigezhuti = useCallback(() => {
    const zhutiliebiao = Object.values(zhutileixing);
    const dangqiansuoyin = zhutiliebiao.indexOf(dangqianzhuti);
    const xiayigesuoyin = (dangqiansuoyin + 1) % zhutiliebiao.length;
    qiehuanzhuti(zhutiliebiao[xiayigesuoyin]);
  }, [dangqianzhuti, qiehuanzhuti]);

  // 检查是否为暗黑主题
  const shifoianheizuti = dangqianzhuti === zhutileixing.ANHEI;

  // 检查是否为明亮主题
  const shifoimingliangzhuti = dangqianzhuti === zhutileixing.MINGLIANG;

  // 根据系统主题自动切换
  const genjuxitongzhutiqiehuan = useCallback(() => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const xitongianheimoshi = window.matchMedia('(prefers-color-scheme: dark)').matches;
      const mubiaozuti = xitongianheimoshi ? zhutileixing.ANHEI : zhutileixing.MINGLIANG;
      
      if (mubiaozuti !== dangqianzhuti) {
        qiehuanzhuti(mubiaozuti);
      }
    }
  }, [dangqianzhuti, qiehuanzhuti]);

  // 监听系统主题变化
  useEventListener(
    'change',
    genjuxitongzhutiqiehuan,
    {
      target: typeof window !== 'undefined' && window.matchMedia 
        ? window.matchMedia('(prefers-color-scheme: dark)')
        : null,
    }
  );

  // 启用/禁用自动切换
  const qiehuanzidongmoshi = useCallback((qiyong) => {
    shezhi_zidongqiehuan(qiyong);
    
    if (qiyong) {
      genjuxitongzhutiqiehuan();
    }
  }, [shezhi_zidongqiehuan, genjuxitongzhutiqiehuan]);

  // 初始化时检查是否需要自动切换
  useEffect(() => {
    if (zidongqiehuan) {
      genjuxitongzhutiqiehuan();
    }
  }, [zidongqiehuan, genjuxitongzhutiqiehuan]);

  // 键盘快捷键支持
  useEventListener('keydown', (event) => {
    // Ctrl/Cmd + Shift + T 切换主题
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
      event.preventDefault();
      qiehuandaoxiayigezhuti();
    }
  });

  return {
    // 状态
    dangqianzhuti,
    shifoianheizuti,
    shifoimingliangzhuti,
    qiehuanzhong,
    zidongqiehuan,
    
    // 切换函数
    qiehuanzhuti,
    qiehuandaomingliangzhuti,
    qiehuandaoanheizuti,
    qiehuandaoxiayigezhuti,
    genjuxitongzhutiqiehuan,
    qiehuanzidongmoshi,
    
    // 主题类型
    zhutileixing,
  };
};

// 主题状态 Hook（只读）
export const shiyongzhutizhuangtai = () => {
  const [dangqianzhuti] = useLocalStorageState(
    ZHUTI_CUNCHU_JIAN,
    {
      defaultValue: zhutileixing.MINGLIANG,
    }
  );

  const [zidongqiehuan] = useLocalStorageState(
    ZIDONG_QIEHUAN_CUNCHU_JIAN,
    {
      defaultValue: false,
    }
  );

  return {
    dangqianzhuti,
    shifoianheizuti: dangqianzhuti === zhutileixing.ANHEI,
    shifoimingliangzhuti: dangqianzhuti === zhutileixing.MINGLIANG,
    zidongqiehuan,
    zhutileixing,
  };
};
